import { Header } from './Header'
import { Sidebar } from './Sidebar'
import { useAppStore } from '@/store/useAppStore'
import { cn } from '@/lib/utils'

export function MainLayout({ children }) {
  const { sidebarCollapsed } = useAppStore()

  return (
    <div className="min-h-screen bg-background">
      <Header />
      <div className="flex">
        <Sidebar />
        <main
          className={cn(
            "flex-1 transition-all duration-300",
            sidebarCollapsed ? "md:ml-16" : "md:ml-64"
          )}
        >
          <div className="container mx-auto">
            {children}
          </div>
        </main>
      </div>
    </div>
  )
}
