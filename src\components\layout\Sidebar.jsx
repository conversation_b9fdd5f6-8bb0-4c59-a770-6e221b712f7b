import { useState } from "react";
import { Link, useLocation } from "@tanstack/react-router";
import {
  Home,
  Users,
  Package,
  Settings,
  ChevronDown,
  ChevronRight,
  UserPlus,
  List,
  PackagePlus,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useAppStore } from "@/store/useAppStore";

const menuItems = [
  {
    title: "Dashboard",
    icon: Home,
    href: "/",
  },
  {
    title: "Users",
    icon: Users,
    href: "/users",
    children: [
      {
        title: "Users List",
        icon: List,
        href: "/users/list",
      },
      {
        title: "Create User",
        icon: UserPlus,
        href: "/users/create",
      },
    ],
  },
  {
    title: "Products",
    icon: Package,
    href: "/products",
    children: [
      {
        title: "Products List",
        icon: List,
        href: "/products/list",
      },
      {
        title: "Create Product",
        icon: PackagePlus,
        href: "/products/create",
      },
    ],
  },
  {
    title: "Settings",
    icon: Settings,
    href: "/settings",
  },
];

function MenuItem({ item, collapsed, level = 0 }) {
  const [isOpen, setIsOpen] = useState(false);
  const location = useLocation();
  const isActive = location.pathname === item.href;
  const hasChildren = item.children && item.children.length > 0;
  const isParentActive =
    hasChildren &&
    item.children.some((child) => location.pathname === child.href);

  const handleClick = () => {
    if (hasChildren) {
      setIsOpen(!isOpen);
    }
  };

  const MenuContent = () => (
    <div className="flex items-center space-x-3">
      <item.icon className="h-5 w-5 flex-shrink-0" />
      {!collapsed && (
        <>
          <span className="flex-1">{item.title}</span>
          {hasChildren && (
            <div className="flex-shrink-0">
              {isOpen ? (
                <ChevronDown className="h-4 w-4" />
              ) : (
                <ChevronRight className="h-4 w-4" />
              )}
            </div>
          )}
        </>
      )}
    </div>
  );

  if (collapsed && !hasChildren) {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant={isActive ? "secondary" : "ghost"}
              className={cn(
                "w-full justify-start h-10 px-3",
                level > 0 && "ml-4"
              )}
              asChild
            >
              <Link to={item.href}>
                <MenuContent />
              </Link>
            </Button>
          </TooltipTrigger>
          <TooltipContent side="right">
            <p>{item.title}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  if (collapsed && hasChildren) {
    return (
      <div className="relative group">
        <Button
          variant={isParentActive ? "secondary" : "ghost"}
          className="w-full justify-start h-10 px-3"
        >
          <MenuContent />
        </Button>
        {/* Submenu popup for collapsed sidebar */}
        <div className="absolute left-full top-0 ml-2 hidden group-hover:block z-[9999]">
          <div className="bg-background border rounded-md shadow-xl py-2 min-w-[200px] backdrop-blur-sm">
            <div className="px-3 py-2 text-sm font-medium border-b">
              {item.title}
            </div>
            {item.children.map((child) => (
              <Button
                key={child.href}
                variant={
                  location.pathname === child.href ? "secondary" : "ghost"
                }
                className="w-full justify-start h-9 px-3 mx-1"
                asChild
              >
                <Link to={child.href}>
                  <child.icon className="h-4 w-4 mr-3" />
                  {child.title}
                </Link>
              </Button>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div>
      {hasChildren ? (
        <Button
          variant={isParentActive ? "secondary" : "ghost"}
          className={cn("w-full justify-start h-10 px-3", level > 0 && "ml-4")}
          onClick={handleClick}
        >
          <MenuContent />
        </Button>
      ) : (
        <Button
          variant={isActive ? "secondary" : "ghost"}
          className={cn("w-full justify-start h-10 px-3", level > 0 && "ml-4")}
          asChild
        >
          <Link to={item.href}>
            <MenuContent />
          </Link>
        </Button>
      )}

      {hasChildren && isOpen && !collapsed && (
        <div className="ml-4 mt-1 space-y-1">
          {item.children.map((child) => (
            <MenuItem
              key={child.href}
              item={child}
              collapsed={collapsed}
              level={level + 1}
            />
          ))}
        </div>
      )}
    </div>
  );
}

export function Sidebar() {
  const { sidebarCollapsed, sidebarOpen, setSidebarOpen } = useAppStore();

  return (
    <>
      {/* Mobile overlay */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 z-40 bg-background/80 backdrop-blur-sm md:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Sidebar */}
      <aside
        className={cn(
          "fixed left-0 top-14 z-40 h-[calc(100vh-3.5rem)] border-r bg-background transition-all duration-300 overflow-visible",
          sidebarCollapsed ? "w-16" : "w-64",
          sidebarOpen ? "translate-x-0" : "-translate-x-full md:translate-x-0"
        )}
      >
        <div className="h-full py-4 overflow-y-auto">
          <nav className="space-y-1 px-3">
            {menuItems.map((item) => (
              <MenuItem
                key={item.href}
                item={item}
                collapsed={sidebarCollapsed}
              />
            ))}
          </nav>
        </div>
      </aside>
    </>
  );
}
