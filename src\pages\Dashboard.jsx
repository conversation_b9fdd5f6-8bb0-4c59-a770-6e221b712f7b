import { useQuery } from '@tanstack/react-query'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Users, Package, TrendingUp, DollarSign } from 'lucide-react'
import { apiClient } from '@/lib/api'

export function Dashboard() {
  const { data: users, isLoading: usersLoading } = useQuery({
    queryKey: ['users'],
    queryFn: () => apiClient.getUsers(),
  })

  const { data: posts, isLoading: postsLoading } = useQuery({
    queryKey: ['posts'],
    queryFn: () => apiClient.getPosts(),
  })

  const stats = [
    {
      title: 'Total Users',
      value: users?.data?.length || 0,
      icon: Users,
      description: 'Active users in the system',
      trend: '+12%',
    },
    {
      title: 'Total Products',
      value: posts?.data?.length || 0,
      icon: Package,
      description: 'Products in inventory',
      trend: '+8%',
    },
    {
      title: 'Revenue',
      value: '$12,345',
      icon: DollarSign,
      description: 'Total revenue this month',
      trend: '+23%',
    },
    {
      title: 'Growth',
      value: '15.3%',
      icon: TrendingUp,
      description: 'Growth rate this quarter',
      trend: '+5%',
    },
  ]

  return (
    <div className="p-6 space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Dashboard</h1>
        <p className="text-muted-foreground">
          Welcome back! Here's what's happening with your business today.
        </p>
      </div>

      {/* Stats Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat) => (
          <Card key={stat.title}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {stat.title}
              </CardTitle>
              <stat.icon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {usersLoading || postsLoading ? '...' : stat.value}
              </div>
              <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                <span>{stat.description}</span>
                <Badge variant="secondary" className="text-xs">
                  {stat.trend}
                </Badge>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Recent Activity */}
      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Recent Users</CardTitle>
            <CardDescription>
              Latest users who joined the platform
            </CardDescription>
          </CardHeader>
          <CardContent>
            {usersLoading ? (
              <div>Loading users...</div>
            ) : (
              <div className="space-y-3">
                {users?.data?.slice(0, 5).map((user) => (
                  <div key={user.id} className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                      <span className="text-sm font-medium">
                        {user.name.charAt(0)}
                      </span>
                    </div>
                    <div className="flex-1">
                      <p className="text-sm font-medium">{user.name}</p>
                      <p className="text-xs text-muted-foreground">{user.email}</p>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Recent Posts</CardTitle>
            <CardDescription>
              Latest posts in the system
            </CardDescription>
          </CardHeader>
          <CardContent>
            {postsLoading ? (
              <div>Loading posts...</div>
            ) : (
              <div className="space-y-3">
                {posts?.data?.slice(0, 5).map((post) => (
                  <div key={post.id} className="space-y-1">
                    <p className="text-sm font-medium line-clamp-1">
                      {post.title}
                    </p>
                    <p className="text-xs text-muted-foreground line-clamp-2">
                      {post.body}
                    </p>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
