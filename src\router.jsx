import {
  createRouter,
  createRootRoute,
  createRoute,
  Outlet,
} from "@tanstack/react-router";
import { MainLayout } from "./components/layout/MainLayout";
import { Dashboard } from "./pages/Dashboard";
import { Users } from "./pages/Users";
import { UsersList } from "./pages/UsersList";
import { CreateUser } from "./pages/CreateUser";
import { Settings } from "./pages/Settings";

// Root route
const rootRoute = createRootRoute({
  component: () => (
    <MainLayout>
      <Outlet />
    </MainLayout>
  ),
});

// Dashboard route
const dashboardRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: "/",
  component: Dashboard,
});

// Users routes
const usersRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: "/users",
  component: Users,
});

const usersListRoute = createRoute({
  getParentRoute: () => usersRoute,
  path: "/list",
  component: UsersList,
});

const usersCreateRoute = createRoute({
  getParentRoute: () => usersRoute,
  path: "/create",
  component: CreateUser,
});

// Products routes
const productsRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: "/products",
  component: () => (
    <div className="p-6">
      <h1 className="text-3xl font-bold mb-4">Products</h1>
      <p className="text-muted-foreground">Manage your products here</p>
    </div>
  ),
});

const productsListRoute = createRoute({
  getParentRoute: () => productsRoute,
  path: "/list",
  component: () => (
    <div className="p-6">
      <h1 className="text-3xl font-bold mb-4">Products List</h1>
      <p className="text-muted-foreground">List of all products</p>
    </div>
  ),
});

const productsCreateRoute = createRoute({
  getParentRoute: () => productsRoute,
  path: "/create",
  component: () => (
    <div className="p-6">
      <h1 className="text-3xl font-bold mb-4">Create Product</h1>
      <p className="text-muted-foreground">Create a new product</p>
    </div>
  ),
});

// Settings route
const settingsRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: "/settings",
  component: Settings,
});

// Create the route tree
const routeTree = rootRoute.addChildren([
  dashboardRoute,
  usersRoute.addChildren([usersListRoute, usersCreateRoute]),
  productsRoute.addChildren([productsListRoute, productsCreateRoute]),
  settingsRoute,
]);

// Create the router
export const router = createRouter({ routeTree });
