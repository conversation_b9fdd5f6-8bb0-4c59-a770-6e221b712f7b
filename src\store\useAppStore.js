import { create } from 'zustand'
import { persist } from 'zustand/middleware'

export const useAppStore = create(
  persist(
    (set, get) => ({
      // Sidebar state
      sidebarCollapsed: false,
      sidebarOpen: false, // For mobile
      
      // User preferences
      theme: 'system',
      
      // Navigation state
      activeMenu: null,
      
      // Actions
      toggleSidebar: () => set((state) => ({ 
        sidebarCollapsed: !state.sidebarCollapsed 
      })),
      
      setSidebarCollapsed: (collapsed) => set({ 
        sidebarCollapsed: collapsed 
      }),
      
      toggleMobileSidebar: () => set((state) => ({ 
        sidebarOpen: !state.sidebarOpen 
      })),
      
      setSidebarOpen: (open) => set({ 
        sidebarOpen: open 
      }),
      
      setActiveMenu: (menu) => set({ 
        activeMenu: menu 
      }),
      
      setTheme: (theme) => set({ 
        theme 
      }),
      
      // Reset function
      reset: () => set({
        sidebarCollapsed: false,
        sidebarOpen: false,
        theme: 'system',
        activeMenu: null,
      }),
    }),
    {
      name: 'app-store',
      partialize: (state) => ({
        sidebarCollapsed: state.sidebarCollapsed,
        theme: state.theme,
      }),
    }
  )
)
